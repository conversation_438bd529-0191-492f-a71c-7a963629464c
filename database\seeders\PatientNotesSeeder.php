<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PatientNote;
use App\Models\PatientManagement;
use App\Models\User;

class PatientNotesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first patient and user for testing
        $patient = PatientManagement::first();
        $user = User::first();

        if (!$patient || !$user) {
            $this->command->info('No patients or users found. Please create some first.');
            return;
        }

        // Create sample notes
        $notes = [
            [
                'patient_id' => $patient->id,
                'user_id' => $user->id,
                'note_type' => 'session',
                'title' => 'Initial Assessment Session',
                'content' => 'Patient presented with symptoms of anxiety and mild depression. Conducted initial assessment using standardized questionnaires. Patient is cooperative and willing to engage in treatment.',
                'is_private' => false,
                'is_important' => false,
                'tags' => ['anxiety', 'assessment'],
                'session_date' => now()->subDays(7),
                'follow_up_required' => true,
                'follow_up_date' => now()->addDays(7),
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
            ],
            [
                'patient_id' => $patient->id,
                'user_id' => $user->id,
                'note_type' => 'treatment',
                'title' => 'Treatment Plan Discussion',
                'content' => 'Discussed cognitive behavioral therapy approach. Patient agreed to weekly sessions. Prescribed relaxation techniques and mindfulness exercises. Patient showed good understanding of treatment goals.',
                'is_private' => false,
                'is_important' => true,
                'tags' => ['CBT', 'treatment-plan'],
                'session_date' => now()->subDays(3),
                'follow_up_required' => false,
                'follow_up_date' => null,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'patient_id' => $patient->id,
                'user_id' => $user->id,
                'note_type' => 'session',
                'title' => 'Progress Review Session',
                'content' => 'Patient reports significant improvement in anxiety levels. Sleep patterns have normalized. Continuing with current treatment approach. Next session scheduled for next week.',
                'is_private' => false,
                'is_important' => false,
                'tags' => ['improvement', 'progress'],
                'session_date' => now()->subDays(1),
                'follow_up_required' => false,
                'follow_up_date' => null,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'patient_id' => $patient->id,
                'user_id' => $user->id,
                'note_type' => 'medication',
                'title' => 'Medication Review',
                'content' => 'Reviewed current medication regimen. Patient reports no side effects. Dosage appears appropriate. Continue current medication for another month before next review.',
                'is_private' => false,
                'is_important' => true,
                'tags' => ['medication', 'review'],
                'session_date' => now()->subDays(5),
                'follow_up_required' => true,
                'follow_up_date' => now()->addDays(30),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
        ];

        foreach ($notes as $noteData) {
            PatientNote::create($noteData);
        }

        $this->command->info('Sample patient notes created successfully!');
    }
}
