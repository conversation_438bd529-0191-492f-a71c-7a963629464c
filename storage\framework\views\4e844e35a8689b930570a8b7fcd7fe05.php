<?php $__env->startSection('content'); ?>
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Therapist Directory</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('therapist-management.dashboard')); ?>">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Therapist Directory</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Statistics Overview-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="h3 mb-1"><?php echo e($directoryStats['total_therapists']); ?></div>
                                    <small>Total Therapists</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h3 mb-1"><?php echo e($directoryStats['active_count']); ?></div>
                                    <small>Active</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h3 mb-1"><?php echo e($directoryStats['on_leave_count']); ?></div>
                                    <small>In active</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h3 mb-1"><?php echo e($directoryStats['pending_approval']); ?></div>
                                    <small>Pending Approval</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Search and Filters-->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="searchTherapist" class="form-label">Search Therapists</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchTherapist" placeholder="Name, email, or license...">
                                    <button class="btn btn-primary" type="button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="statusFilter" class="form-label">Status</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>

                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="specializationFilter" class="form-label">Specialization</label>
                                <select class="form-select" id="specializationFilter">
                                    <option value="">All Specializations</option>
                                    <option value="clinical">Clinical Psychology</option>
                                    <option value="family">Family Therapy</option>
                                    <option value="child">Child Psychology</option>
                                    <option value="addiction">Addiction Counseling</option>
                                    <option value="trauma">Trauma Therapy</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="clinicFilter" class="form-label">Clinic</label>
                                <select class="form-select" id="clinicFilter">
                                    <option value="">All Clinics</option>
                                    <option value="nairobi">Nairobi Central Clinic</option>
                                    <option value="mombasa">Mombasa Health Center</option>
                                    <option value="kisumu">Kisumu Medical Center</option>
                                    <option value="eldoret">Eldoret Wellness Center</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> Apply Filters
                            </button>
                            <button class="btn btn-outline-secondary me-2" onclick="clearFilters()">
                                <i class="bi bi-x-circle"></i> Clear
                            </button>
                            <a href="<?php echo e(route('therapist-management.directory.create')); ?>" class="btn btn-success">
                                <i class="bi bi-person-plus"></i> Add New Therapist
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Therapist List-->
            <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $therapists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $therapist): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-6 col-xl-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-auto">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                        <?php echo e(substr($therapist->name, 0, 1)); ?><?php echo e(substr(explode(' ', $therapist->name)[1] ?? '', 0, 1)); ?>

                                    </div>
                                </div>
                                <div class="col">
                                    <h5 class="mb-1"><?php echo e($therapist->name); ?></h5>
                                    <p class="text-muted mb-1"><?php echo e($therapist->email); ?></p>
                                    <div class="text-warning">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= floor($therapist->rating)): ?>
                                                <i class="bi bi-star-fill"></i>
                                            <?php elseif($i <= $therapist->rating): ?>
                                                <i class="bi bi-star-half"></i>
                                            <?php else: ?>
                                                <i class="bi bi-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                        <small class="text-muted ms-1">(<?php echo e($therapist->rating); ?>)</small>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <?php
                                        $statusClass = match(strtolower($therapist->status)) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-warning',
                                            'on leave' => 'bg-info',
                                            default => 'bg-danger'
                                        };
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?>"><?php echo e($therapist->status); ?></span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="badge bg-primary me-1"><?php echo e($therapist->specialization); ?></span>
                                <span class="badge bg-light text-dark me-1"><?php echo e($therapist->education_level); ?></span>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary"><?php echo e($therapist->patients_count); ?></div>
                                    <small class="text-muted">Patients</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success"><?php echo e($therapist->experience_years); ?>y</div>
                                    <small class="text-muted">Experience</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info"><?php echo e($therapist->availability); ?></div>
                                    <small class="text-muted">Availability</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-building me-1"></i><?php echo e($therapist->clinic); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i><?php echo e($therapist->phone ?? 'Not provided'); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>Last updated: <?php echo e($therapist->last_active->diffForHumans()); ?>

                                </small>
                            </div>

                            <div class="d-flex flex-wrap gap-2">
                                <a href="<?php echo e(route('therapist-management.directory.view', $therapist->id)); ?>" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="<?php echo e(route('therapist-management.directory.edit', $therapist->id)); ?>" class="btn btn-sm btn-success">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="<?php echo e(route('therapist-management.clinics.assign')); ?>" class="btn btn-sm btn-warning">
                                    <i class="bi bi-building"></i> Assign
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Therapists Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or add a new therapist.</p>
                        <a href="<?php echo e(route('therapist-management.directory.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add New Therapist
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!--begin::Pagination-->
            <?php if(isset($therapists) && method_exists($therapists, 'hasPages') && $therapists->hasPages()): ?>
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Showing <?php echo e($therapists->firstItem()); ?> to <?php echo e($therapists->lastItem()); ?> of <?php echo e($therapists->total()); ?> therapists
                    </p>
                </div>
                <div>
                    <?php echo e($therapists->appends(request()->query())->links('custom.pagination')); ?>

                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</main>

<script>
function applyFilters() {
    const search = document.getElementById('searchTherapist').value;
    const status = document.getElementById('statusFilter').value;
    const specialization = document.getElementById('specializationFilter').value;
    const clinic = document.getElementById('clinicFilter').value;

    console.log('Applying filters:', { search, status, specialization, clinic });
    alert('Filters applied! (In production, this would filter the results)');
}

function clearFilters() {
    document.getElementById('searchTherapist').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('specializationFilter').value = '';
    document.getElementById('clinicFilter').value = '';

    alert('Filters cleared!');
}

// Real-time search functionality
document.getElementById('searchTherapist').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    console.log('Searching for:', searchTerm);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/management/therapist/directory/index.blade.php ENDPATH**/ ?>