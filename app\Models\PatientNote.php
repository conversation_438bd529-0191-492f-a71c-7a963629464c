<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PatientNote extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'patient_id',
        'user_id',
        'note_type',
        'title',
        'content',
        'is_private',
        'is_important',
        'tags',
        'session_date',
        'follow_up_required',
        'follow_up_date',
        'attachments',
    ];

    protected $casts = [
        'session_date' => 'datetime',
        'follow_up_date' => 'datetime',
        'follow_up_required' => 'boolean',
        'is_private' => 'boolean',
        'is_important' => 'boolean',
        'tags' => 'array',
        'attachments' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the patient that owns the note.
     */
    public function patient()
    {
        return $this->belongsTo(PatientManagement::class, 'patient_id');
    }

    /**
     * Get the user who created the note.
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
